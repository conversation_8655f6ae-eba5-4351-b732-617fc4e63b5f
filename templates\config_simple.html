{% extends "base.html" %}

{% block title %}简化配置管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs me-2"></i>产品配置管理
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="configMode" id="simpleMode" autocomplete="off" checked>
                            <label class="btn btn-outline-primary" for="simpleMode">
                                <i class="fas fa-user me-1"></i>简单模式
                            </label>
                            
                            <input type="radio" class="btn-check" name="configMode" id="advancedMode" autocomplete="off">
                            <label class="btn btn-outline-secondary" for="advancedMode">
                                <i class="fas fa-code me-1"></i>专业模式
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 简单配置模式 -->
                    <div id="simpleConfigPanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-plus-circle me-2"></i>添加新产品
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="addProductForm">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="productKey" class="form-label">
                                                            <i class="fas fa-key me-1"></i>产品密钥 *
                                                        </label>
                                                        <input type="text" class="form-control" id="productKey" required 
                                                               placeholder="例如: a1AbCdEfGhI">
                                                        <div class="form-text">阿里云IoT平台分配的产品密钥</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="productName" class="form-label">
                                                            <i class="fas fa-tag me-1"></i>产品名称 *
                                                        </label>
                                                        <input type="text" class="form-control" id="productName" required 
                                                               placeholder="例如: 充电桩设备">
                                                        <div class="form-text">便于识别的产品名称</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="productSecret" class="form-label">
                                                            <i class="fas fa-lock me-1"></i>产品密钥 (可选)
                                                        </label>
                                                        <input type="text" class="form-control" id="productSecret" 
                                                               placeholder="产品密钥，如果需要的话">
                                                        <div class="form-text">某些认证方式需要产品密钥</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="region" class="form-label">
                                                            <i class="fas fa-globe me-1"></i>区域
                                                        </label>
                                                        <select class="form-select" id="region">
                                                            <option value="cn-shanghai">华东2 (上海)</option>
                                                            <option value="cn-beijing">华北2 (北京)</option>
                                                            <option value="cn-shenzhen">华南1 (深圳)</option>
                                                            <option value="cn-hangzhou">华东1 (杭州)</option>
                                                        </select>
                                                        <div class="form-text">选择IoT平台所在区域</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-plus me-1"></i>添加产品
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-question-circle me-2"></i>帮助说明
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <h6>如何获取产品密钥？</h6>
                                        <ol class="small">
                                            <li>登录阿里云IoT平台控制台</li>
                                            <li>进入"设备管理" → "产品"</li>
                                            <li>选择您的产品</li>
                                            <li>在产品详情页面查看ProductKey</li>
                                        </ol>
                                        
                                        <h6 class="mt-3">注意事项</h6>
                                        <ul class="small">
                                            <li>产品密钥是唯一标识符</li>
                                            <li>请确保密钥正确无误</li>
                                            <li>区域选择要与IoT平台一致</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 现有产品列表 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-list me-2"></i>已配置产品列表
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="productsList">
                                            <div class="text-center text-muted">
                                                <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 专业配置模式 -->
                    <div id="advancedConfigPanel" style="display: none;">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>专业模式</strong> - 直接编辑JSON配置文件，请确保格式正确
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="configEditor">配置内容 (JSON格式)</label>
                                    <textarea id="configEditor" class="form-control" rows="25" 
                                              style="font-family: 'Courier New', monospace;"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 配置验证结果 -->
                        <div id="validationResult" class="mt-3" style="display: none;">
                            <!-- 验证结果将在这里显示 -->
                        </div>
                        
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" onclick="saveConfig()">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="loadConfig()">
                                <i class="fas fa-sync me-1"></i>重新加载
                            </button>
                            <button type="button" class="btn btn-info" onclick="validateConfig()">
                                <i class="fas fa-check me-1"></i>验证配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
let currentConfig = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProductsList();
    
    // 模式切换事件
    document.getElementById('simpleMode').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('simpleConfigPanel').style.display = 'block';
            document.getElementById('advancedConfigPanel').style.display = 'none';
        }
    });
    
    document.getElementById('advancedMode').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('simpleConfigPanel').style.display = 'none';
            document.getElementById('advancedConfigPanel').style.display = 'block';
            loadConfig(); // 加载配置到编辑器
        }
    });
    
    // 添加产品表单提交事件
    document.getElementById('addProductForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addProduct();
    });
});

// 加载产品列表
function loadProductsList() {
    fetch('/api/config')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            displayProductsList(data);
            currentConfig = data;
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            document.getElementById('productsList').innerHTML =
                '<div class="alert alert-danger">加载配置失败: ' + error.message + '</div>';
        });
}

// 显示产品列表
function displayProductsList(config) {
    const container = document.getElementById('productsList');
    
    if (!config.products || Object.keys(config.products).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-inbox me-2"></i>暂无配置的产品
            </div>
        `;
        return;
    }
    
    let html = '<div class="row">';
    
    Object.entries(config.products).forEach(([productKey, productConfig]) => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-cube me-2"></i>${productConfig.name || productKey}
                        </h6>
                        <p class="card-text">
                            <small class="text-muted">产品密钥: <code>${productKey}</code></small><br>
                            <small class="text-muted">区域: ${productConfig.region || 'cn-shanghai'}</small>
                        </p>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeProduct('${productKey}')">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// 添加产品
function addProduct() {
    const productKey = document.getElementById('productKey').value.trim();
    const productName = document.getElementById('productName').value.trim();
    const productSecret = document.getElementById('productSecret').value.trim();
    const region = document.getElementById('region').value;
    
    if (!productKey || !productName) {
        alert('请填写必填字段');
        return;
    }
    
    // 构建新产品配置
    const newProductConfig = {
        name: productName,
        region: region
    };
    
    if (productSecret) {
        newProductConfig.product_secret = productSecret;
    }
    
    // 更新配置
    if (!currentConfig.products) {
        currentConfig.products = {};
    }
    
    currentConfig.products[productKey] = newProductConfig;
    
    // 保存配置
    saveConfigData(currentConfig)
        .then(() => {
            // 清空表单
            document.getElementById('addProductForm').reset();
            // 重新加载列表
            loadProductsList();
            // 显示成功消息
            showMessage('产品添加成功', 'success');
        })
        .catch(error => {
            showMessage('添加产品失败: ' + error.message, 'danger');
        });
}

// 删除产品
function removeProduct(productKey) {
    if (!confirm(`确定要删除产品 "${productKey}" 吗？`)) {
        return;
    }
    
    if (currentConfig.products && currentConfig.products[productKey]) {
        delete currentConfig.products[productKey];
        
        saveConfigData(currentConfig)
            .then(() => {
                loadProductsList();
                showMessage('产品删除成功', 'success');
            })
            .catch(error => {
                showMessage('删除产品失败: ' + error.message, 'danger');
            });
    }
}

// 保存配置数据
function saveConfigData(configData) {
    return fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(configData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('保存失败');
        }
        return response.json();
    });
}

// 显示消息
function showMessage(message, type) {
    // 创建消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 专业模式相关函数
function loadConfig() {
    fetch('/api/config')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            document.getElementById('configEditor').value = JSON.stringify(data, null, 2);
            currentConfig = data;
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            showMessage('加载配置失败: ' + error.message, 'danger');
        });
}

function saveConfig() {
    const configText = document.getElementById('configEditor').value;
    
    try {
        const configData = JSON.parse(configText);
        saveConfigData(configData)
            .then(() => {
                showMessage('配置保存成功', 'success');
                currentConfig = configData;
            })
            .catch(error => {
                showMessage('保存配置失败: ' + error.message, 'danger');
            });
    } catch (error) {
        showMessage('JSON格式错误: ' + error.message, 'danger');
    }
}

function validateConfig() {
    const configText = document.getElementById('configEditor').value;
    const resultDiv = document.getElementById('validationResult');
    
    try {
        const configData = JSON.parse(configText);
        resultDiv.innerHTML = '<div class="alert alert-success">配置格式正确</div>';
        resultDiv.style.display = 'block';
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger">JSON格式错误: ' + error.message + '</div>';
        resultDiv.style.display = 'block';
    }
}
</script>
{% endblock %}

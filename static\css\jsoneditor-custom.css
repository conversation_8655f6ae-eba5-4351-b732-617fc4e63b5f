/* JSONEditor 自定义样式 - 与现有页面保持一致 */

/* 主容器样式 */
.jsoneditor {
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 工具栏样式 */
.jsoneditor-menu {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 4px 4px 0 0 !important;
}

/* 按钮样式 */
.jsoneditor-menu > button,
.jsoneditor-menu > .jsoneditor-modes > button {
    background: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 3px !important;
    color: #495057 !important;
    font-size: 12px !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
}

.jsoneditor-menu > button:hover,
.jsoneditor-menu > .jsoneditor-modes > button:hover {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

.jsoneditor-menu > button.jsoneditor-selected,
.jsoneditor-menu > .jsoneditor-modes > button.jsoneditor-selected {
    background: #007bff !important;
    color: #fff !important;
    border-color: #007bff !important;
}

/* 搜索框样式 */
.jsoneditor-search input {
    border: 1px solid #ced4da !important;
    border-radius: 3px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
}

.jsoneditor-search input:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    outline: none !important;
}

/* 树形视图样式 */
.jsoneditor-tree {
    background: #fff !important;
    font-family: 'Courier New', monospace !important;
}

/* 代码编辑器样式 */
.jsoneditor-code .ace_editor {
    font-family: 'Courier New', monospace !important;
    font-size: 13px !important;
}

/* 状态栏样式 */
.jsoneditor-statusbar {
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
    color: #6c757d !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
}

/* 错误提示样式 */
.jsoneditor-validation-errors {
    background: #f8d7da !important;
    border: 1px solid #f5c6cb !important;
    border-radius: 3px !important;
    color: #721c24 !important;
    padding: 8px !important;
    margin: 4px 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jsoneditor {
        height: 400px !important;
    }
    
    .jsoneditor-menu {
        flex-wrap: wrap !important;
    }
    
    .jsoneditor-menu > button,
    .jsoneditor-menu > .jsoneditor-modes > button {
        font-size: 11px !important;
        padding: 3px 6px !important;
    }
}

/* 深色主题兼容 */
@media (prefers-color-scheme: dark) {
    .jsoneditor {
        background: #2d3748 !important;
        border-color: #4a5568 !important;
    }
    
    .jsoneditor-menu {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
        border-bottom-color: #4a5568 !important;
    }
    
    .jsoneditor-tree {
        background: #2d3748 !important;
        color: #e2e8f0 !important;
    }
    
    .jsoneditor-statusbar {
        background: #1a202c !important;
        border-top-color: #4a5568 !important;
        color: #a0aec0 !important;
    }
}

/* 确保与Bootstrap样式兼容 */
.jsoneditor * {
    box-sizing: border-box !important;
}

/* 修复可能的z-index问题 */
.jsoneditor-modal,
.jsoneditor-contextmenu {
    z-index: 1060 !important;
}

/* 配置编辑器容器样式 */
#configEditor {
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 验证结果容器 */
#validationResult {
    display: none;
}

/* 专业配置模式面板 */
#advancedConfigPanel {
    display: none;
}

/* 通知容器 */
#notification {
    z-index: 1055;
}

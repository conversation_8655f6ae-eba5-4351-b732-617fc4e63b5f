# JSONEditor 集成文档

## 概述

本项目已成功集成 JSONEditor 库（版本 10.4.1），用于替换原有的 textarea 元素，提供更强大的 JSON 编辑功能。

## 集成的文件

### 库文件
- `static/libs/js/jsoneditor.min.js` - JSONEditor JavaScript 库
- `static/libs/css/jsoneditor.min.css` - JSONEditor CSS 样式
- `static/css/jsoneditor-custom.css` - 自定义样式，确保与现有页面保持一致

### 修改的模板文件
- `templates/config_management.html` - 服务器产品配置管理页面
- `templates/config_simple.html` - 简化配置管理页面

## 功能特性

### JSONEditor 模式
- **Tree 模式**（默认）：树形结构显示，便于可视化编辑
- **Code 模式**：代码编辑器，支持语法高亮
- **Text 模式**：纯文本编辑
- **Preview 模式**：只读预览模式

### 核心功能
- ✅ 语法检测和验证
- ✅ 搜索和替换
- ✅ 撤销/重做操作
- ✅ 导航栏和状态栏
- ✅ 错误提示
- ✅ 响应式设计

### 样式特性
- ✅ 与现有页面样式保持一致
- ✅ 支持深色主题
- ✅ 移动端适配
- ✅ Bootstrap 兼容

## 使用方法

### config_management.html
页面加载时自动初始化 JSONEditor，默认使用 tree 模式。

### config_simple.html
在专业模式下初始化 JSONEditor，支持模式切换。

## 配置选项

```javascript
const options = {
    mode: 'tree',                    // 默认模式
    modes: ['tree', 'code', 'text', 'preview'], // 可用模式
    search: true,                    // 启用搜索
    history: true,                   // 启用历史记录
    navigationBar: true,             // 显示导航栏
    statusBar: true,                 // 显示状态栏
    onError: function (err) {        // 错误处理
        console.error('JSONEditor error:', err);
    },
    onChange: function() {           // 变更监听
        // 延迟验证，避免频繁验证
        clearTimeout(jsonEditor.validateTimeout);
        jsonEditor.validateTimeout = setTimeout(validateConfig, 500);
    }
};
```

## API 使用

### 设置 JSON 数据
```javascript
jsonEditor.set(data);
```

### 获取 JSON 数据
```javascript
const data = jsonEditor.get();
```

### 验证 JSON
```javascript
try {
    const data = jsonEditor.get();
    // 数据有效
} catch (error) {
    // 处理验证错误
}
```

## 自定义样式

自定义样式文件 `static/css/jsoneditor-custom.css` 包含：
- 主容器样式调整
- 工具栏和按钮样式
- 搜索框样式
- 错误提示样式
- 响应式设计
- 深色主题支持

## 兼容性

- ✅ Chrome
- ✅ Firefox  
- ✅ Safari
- ✅ Edge
- ✅ 移动端浏览器

## 注意事项

1. JSONEditor 库已本地化，无需依赖外部 CDN
2. 自动验证功能会在用户编辑时延迟 500ms 触发，避免频繁验证
3. 错误提示会显示在编辑器下方的验证结果区域
4. 支持键盘快捷键操作（Ctrl+Z 撤销，Ctrl+Y 重做等）

## 故障排除

### 常见问题
1. **编辑器不显示**：检查 JavaScript 控制台是否有错误
2. **样式异常**：确认 CSS 文件加载顺序正确
3. **功能异常**：检查 JSONEditor 库文件是否完整加载

### 调试方法
```javascript
// 检查 JSONEditor 是否正确初始化
console.log(jsonEditor);

// 检查当前数据
console.log(jsonEditor.get());
```
